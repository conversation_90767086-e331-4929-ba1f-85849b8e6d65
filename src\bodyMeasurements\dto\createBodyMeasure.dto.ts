import { ApiProperty } from '@nestjs/swagger';
import { IMeasurementGoal, IMeasurementRecord } from '../interfaces/bodyMeasure.interfaces';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateBodyMeasureDto {

    @ApiProperty({
        description: 'User ID associated with this body measurement',
        example: '507f1f77bcf86cd799439012',
    })
    @IsNotEmpty()
    userId: string;

    @ApiProperty({
        description: 'Measurement record for the neck',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    neck: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the chest',
        example: {
            value: 40,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    chest: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the shoulder',
        example: {
            value: 42,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    shoulder: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left arm',
        example: {
            value: 32,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    leftArm: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right arm',
        example: {
            value: 32,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    rightArm: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the waist',
        example: {
            value: 30,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    waist: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the abdomen',
        example: {
            value: 34,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    abdomen: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the hip',
        example: {
            value: 38,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    hip: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left thigh',
        example: {
            value: 48,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    leftThigh: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right thigh',
        example: {
            value: 48,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    rightThigh: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left calf',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    leftCalf: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right calf',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    @IsNotEmpty()
    rightCalf: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement goal for a specific body part',
        example: {
            target: 32,
            bodyPart: 'waist',
        },
    })
    @IsNotEmpty()
    goals: IMeasurementGoal;
    @ApiProperty({
        description: 'Custom measurements for additional body parts',
        example: new Map([['bicep', { value: 30, recordedAt: '2024-01-15T10:30:00.000Z' }]]),
    })
    @IsOptional()
    customMeasurements: Map<string, IMeasurementRecord>;

}
