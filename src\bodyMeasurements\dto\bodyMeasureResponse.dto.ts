import { ApiProperty } from '@nestjs/swagger';
import { IMeasurementGoal, IMeasurementRecord } from '../interfaces/bodyMeasure.interfaces';

export class BodyMeasureResponseDto {
    @ApiProperty({
        description: 'Unique identifier for the body measurement record',
        example: '507f1f77bcf86cd799439011',
    })
    _id: string;

    @ApiProperty({
        description: 'User ID associated with this body measurement',
        example: '507f1f77bcf86cd799439012',
    })
    userId: string;

    @ApiProperty({
        description: 'Measurement record for the neck',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    neck: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the chest',
        example: {
            value: 40,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    chest: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the shoulder',
        example: {
            value: 42,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    shoulder: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left arm',
        example: {
            value: 32,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    leftArm: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right arm',
        example: {
            value: 32,
            // recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    rightArm: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the waist',
        example: {
            value: 30,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    waist: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the abdomen',
        example: {
            value: 34,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    abdomen: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the hip',
        example: {
            value: 38,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    hip: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left thigh',
        example: {
            value: 48,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    leftThigh: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right thigh',
        example: {
            value: 48,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    rightThigh: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left calf',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    leftCalf: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right calf',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    rightCalf: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement goal for a specific body part',
        example: {
            target: 32,
            bodyPart: 'waist',
        },
    })
    goals: IMeasurementGoal;

    @ApiProperty({
        description: 'Custom measurements for additional body parts',
        example: {
            'bicep': { value: 30, recordedAt: '2024-01-15T10:30:00.000Z' },
            'forearm': { value: 25, recordedAt: '2024-01-15T10:30:00.000Z' }
        },
    })
    customMeasurements?: Map<string, IMeasurementRecord>;

    @ApiProperty({
        description: 'Timestamp when the body measurement was created',
        example: '2024-01-15T10:30:00.000Z',
    })
    createdAt: Date;

    @ApiProperty({
        description: 'Timestamp when the body measurement was last updated',
        example: '2024-01-15T10:30:00.000Z',
    })
    updatedAt: Date;
}
